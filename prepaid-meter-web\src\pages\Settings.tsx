import { useState } from 'react'
import {
  Save,
  Bell,
  Shield,
  Database,
  DollarSign,
  Settings as SettingsIcon,
  Download,
  Upload
} from 'lucide-react'

const Settings = () => {
  const [activeTab, setActiveTab] = useState('general')
  const [settings, setSettings] = useState({
    general: {
      companyName: 'MeterPro Solutions',
      timezone: 'Africa/Lagos',
      currency: 'NGN',
      language: 'en',
      dateFormat: 'DD/MM/YYYY'
    },
    notifications: {
      emailAlerts: true,
      smsAlerts: false,
      lowBalanceThreshold: 500,
      faultyMeterAlerts: true,
      recoveryCompleteAlerts: true,
      dailyReports: true
    },
    tariff: {
      singlePhaseRate: 45.50,
      threePhaseRate: 52.00,
      minimumBalance: 100,
      reconnectionFee: 1000,
      serviceFee: 50
    },
    system: {
      autoBackup: true,
      backupFrequency: 'daily',
      dataRetention: 365,
      maintenanceMode: false,
      debugMode: false
    },
    security: {
      sessionTimeout: 30,
      passwordExpiry: 90,
      twoFactorAuth: false,
      loginAttempts: 5,
      ipWhitelist: ''
    }
  })

  const tabs = [
    { id: 'general', name: 'General', icon: SettingsIcon },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'tariff', name: 'Tariff & Billing', icon: DollarSign },
    { id: 'system', name: 'System', icon: Database },
    { id: 'security', name: 'Security', icon: Shield },
  ]

  const handleSettingChange = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value
      }
    }))
  }

  const handleSave = () => {
    // In a real app, this would save to backend
    console.log('Saving settings:', settings)
    alert('Settings saved successfully!')
  }

  const handleExportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    
    const exportFileDefaultName = 'meterpro-settings.json'
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="label">Company Name</label>
          <input
            type="text"
            className="input"
            value={settings.general.companyName}
            onChange={(e) => handleSettingChange('general', 'companyName', e.target.value)}
          />
        </div>
        <div>
          <label className="label">Timezone</label>
          <select
            className="input"
            value={settings.general.timezone}
            onChange={(e) => handleSettingChange('general', 'timezone', e.target.value)}
          >
            <option value="Africa/Lagos">Africa/Lagos</option>
            <option value="Africa/Abuja">Africa/Abuja</option>
            <option value="UTC">UTC</option>
          </select>
        </div>
        <div>
          <label className="label">Currency</label>
          <select
            className="input"
            value={settings.general.currency}
            onChange={(e) => handleSettingChange('general', 'currency', e.target.value)}
          >
            <option value="NGN">Nigerian Naira (₦)</option>
            <option value="USD">US Dollar ($)</option>
            <option value="EUR">Euro (€)</option>
          </select>
        </div>
        <div>
          <label className="label">Date Format</label>
          <select
            className="input"
            value={settings.general.dateFormat}
            onChange={(e) => handleSettingChange('general', 'dateFormat', e.target.value)}
          >
            <option value="DD/MM/YYYY">DD/MM/YYYY</option>
            <option value="MM/DD/YYYY">MM/DD/YYYY</option>
            <option value="YYYY-MM-DD">YYYY-MM-DD</option>
          </select>
        </div>
      </div>
    </div>
  )

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-gray-900">Email Alerts</h4>
            <p className="text-sm text-gray-500">Receive notifications via email</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={settings.notifications.emailAlerts}
              onChange={(e) => handleSettingChange('notifications', 'emailAlerts', e.target.checked)}
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-gray-900">SMS Alerts</h4>
            <p className="text-sm text-gray-500">Receive notifications via SMS</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={settings.notifications.smsAlerts}
              onChange={(e) => handleSettingChange('notifications', 'smsAlerts', e.target.checked)}
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>

        <div>
          <label className="label">Low Balance Alert Threshold (₦)</label>
          <input
            type="number"
            className="input"
            value={settings.notifications.lowBalanceThreshold}
            onChange={(e) => handleSettingChange('notifications', 'lowBalanceThreshold', Number(e.target.value))}
          />
        </div>
      </div>
    </div>
  )

  const renderTariffSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="label">Single Phase Rate (₦/kWh)</label>
          <input
            type="number"
            step="0.01"
            className="input"
            value={settings.tariff.singlePhaseRate}
            onChange={(e) => handleSettingChange('tariff', 'singlePhaseRate', Number(e.target.value))}
          />
        </div>
        <div>
          <label className="label">Three Phase Rate (₦/kWh)</label>
          <input
            type="number"
            step="0.01"
            className="input"
            value={settings.tariff.threePhaseRate}
            onChange={(e) => handleSettingChange('tariff', 'threePhaseRate', Number(e.target.value))}
          />
        </div>
        <div>
          <label className="label">Minimum Balance (₦)</label>
          <input
            type="number"
            className="input"
            value={settings.tariff.minimumBalance}
            onChange={(e) => handleSettingChange('tariff', 'minimumBalance', Number(e.target.value))}
          />
        </div>
        <div>
          <label className="label">Reconnection Fee (₦)</label>
          <input
            type="number"
            className="input"
            value={settings.tariff.reconnectionFee}
            onChange={(e) => handleSettingChange('tariff', 'reconnectionFee', Number(e.target.value))}
          />
        </div>
      </div>
    </div>
  )

  const renderSystemSettings = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-gray-900">Auto Backup</h4>
            <p className="text-sm text-gray-500">Automatically backup system data</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={settings.system.autoBackup}
              onChange={(e) => handleSettingChange('system', 'autoBackup', e.target.checked)}
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>

        <div>
          <label className="label">Data Retention (days)</label>
          <input
            type="number"
            className="input"
            value={settings.system.dataRetention}
            onChange={(e) => handleSettingChange('system', 'dataRetention', Number(e.target.value))}
          />
        </div>

        <div className="flex space-x-4">
          <button className="btn btn-outline">
            <Download className="h-4 w-4 mr-2" />
            Backup Now
          </button>
          <button className="btn btn-outline">
            <Upload className="h-4 w-4 mr-2" />
            Restore Backup
          </button>
        </div>
      </div>
    </div>
  )

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="label">Session Timeout (minutes)</label>
          <input
            type="number"
            className="input"
            value={settings.security.sessionTimeout}
            onChange={(e) => handleSettingChange('security', 'sessionTimeout', Number(e.target.value))}
          />
        </div>
        <div>
          <label className="label">Max Login Attempts</label>
          <input
            type="number"
            className="input"
            value={settings.security.loginAttempts}
            onChange={(e) => handleSettingChange('security', 'loginAttempts', Number(e.target.value))}
          />
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div>
          <h4 className="text-sm font-medium text-gray-900">Two-Factor Authentication</h4>
          <p className="text-sm text-gray-500">Add an extra layer of security</p>
        </div>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            className="sr-only peer"
            checked={settings.security.twoFactorAuth}
            onChange={(e) => handleSettingChange('security', 'twoFactorAuth', e.target.checked)}
          />
          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
        </label>
      </div>
    </div>
  )

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general': return renderGeneralSettings()
      case 'notifications': return renderNotificationSettings()
      case 'tariff': return renderTariffSettings()
      case 'system': return renderSystemSettings()
      case 'security': return renderSecuritySettings()
      default: return renderGeneralSettings()
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Settings</h2>
          <p className="text-gray-600 mt-1">Manage your system configuration and preferences</p>
        </div>
        
        <div className="flex space-x-2 mt-4 sm:mt-0">
          <button onClick={handleExportSettings} className="btn btn-outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
          <button onClick={handleSave} className="btn btn-primary">
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <nav className="space-y-1">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <Icon className="mr-3 h-5 w-5" />
                  {tab.name}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">
              {tabs.find(tab => tab.id === activeTab)?.name} Settings
            </h3>
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  )
}

export default Settings
