import { useState } from 'react'
import { Search, Filter, Plus, Eye, Edit, Trash2, MapPin } from 'lucide-react'
import type { Meter, MeterFilter } from '../types/meter'

interface MetersProps {
  meters: Meter[]
  setMeters: (meters: Meter[]) => void
}

const Meters = ({ meters, setMeters }: MetersProps) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [filter, setFilter] = useState<MeterFilter>({})
  const [showAddModal, setShowAddModal] = useState(false)
  const [selectedMeter, setSelectedMeter] = useState<Meter | null>(null)

  const filteredMeters = meters.filter(meter => {
    const matchesSearch = !searchTerm || 
      meter.serialNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      meter.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      meter.customerAddress.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = !filter.status || meter.status === filter.status
    const matchesType = !filter.meterType || meter.meterType === filter.meterType
    
    return matchesSearch && matchesStatus && matchesType
  })

  const getStatusColor = (status: Meter['status']) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'inactive': return 'bg-gray-100 text-gray-800'
      case 'faulty': return 'bg-red-100 text-red-800'
      case 'recovery-needed': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const handleDeleteMeter = (meterId: string) => {
    if (window.confirm('Are you sure you want to delete this meter?')) {
      setMeters(meters.filter(m => m.id !== meterId))
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Meters Management</h2>
          <p className="text-gray-600 mt-1">Manage all prepaid electricity meters</p>
        </div>
        <button 
          onClick={() => setShowAddModal(true)}
          className="btn btn-primary mt-4 sm:mt-0"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add New Meter
        </button>
      </div>

      {/* Search and Filters */}
      <div className="card">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search meters..."
              className="input pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <select 
            className="input"
            value={filter.status || ''}
            onChange={(e) => setFilter({...filter, status: e.target.value as Meter['status'] || undefined})}
          >
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="faulty">Faulty</option>
            <option value="recovery-needed">Recovery Needed</option>
          </select>

          <select 
            className="input"
            value={filter.meterType || ''}
            onChange={(e) => setFilter({...filter, meterType: e.target.value as Meter['meterType'] || undefined})}
          >
            <option value="">All Types</option>
            <option value="single-phase">Single Phase</option>
            <option value="three-phase">Three Phase</option>
          </select>

          <button className="btn btn-outline">
            <Filter className="h-4 w-4 mr-2" />
            More Filters
          </button>
        </div>
      </div>

      {/* Meters Table */}
      <div className="card">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Meter Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Balance
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Consumption
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredMeters.map((meter) => (
                <tr key={meter.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {meter.serialNumber}
                      </div>
                      <div className="text-sm text-gray-500">
                        {meter.meterType} • Installed {meter.installationDate.toLocaleDateString()}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {meter.customerName}
                      </div>
                      <div className="text-sm text-gray-500 flex items-center">
                        <MapPin className="h-3 w-3 mr-1" />
                        {meter.location.address}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(meter.status)}`}>
                      {meter.status.replace('-', ' ')}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      ₦{meter.balance.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-500">
                      Last recharge: {meter.lastRecharge?.toLocaleDateString() || 'Never'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {meter.dailyConsumption.toFixed(1)} kWh/day
                    </div>
                    <div className="text-sm text-gray-500">
                      {meter.monthlyConsumption.toFixed(1)} kWh/month
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button 
                        onClick={() => setSelectedMeter(meter)}
                        className="text-blue-600 hover:text-blue-900"
                        title="View Details"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button 
                        className="text-green-600 hover:text-green-900"
                        title="Edit Meter"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button 
                        onClick={() => handleDeleteMeter(meter.id)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete Meter"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredMeters.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500">
              No meters found matching your criteria.
            </div>
          </div>
        )}
      </div>

      {/* Meter Details Modal */}
      {selectedMeter && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={() => setSelectedMeter(null)}></div>
            <div className="relative bg-white rounded-lg max-w-2xl w-full p-6">
              <h3 className="text-lg font-semibold mb-4">Meter Details</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="label">Serial Number</label>
                  <p className="text-gray-900">{selectedMeter.serialNumber}</p>
                </div>
                <div>
                  <label className="label">Customer Name</label>
                  <p className="text-gray-900">{selectedMeter.customerName}</p>
                </div>
                <div>
                  <label className="label">Status</label>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(selectedMeter.status)}`}>
                    {selectedMeter.status.replace('-', ' ')}
                  </span>
                </div>
                <div>
                  <label className="label">Balance</label>
                  <p className="text-gray-900">₦{selectedMeter.balance.toLocaleString()}</p>
                </div>
                <div>
                  <label className="label">Current Reading</label>
                  <p className="text-gray-900">{selectedMeter.currentReading} kWh</p>
                </div>
                <div>
                  <label className="label">Daily Consumption</label>
                  <p className="text-gray-900">{selectedMeter.dailyConsumption} kWh</p>
                </div>
                <div className="col-span-2">
                  <label className="label">Address</label>
                  <p className="text-gray-900">{selectedMeter.location.address}</p>
                </div>
              </div>
              <div className="mt-6 flex justify-end">
                <button 
                  onClick={() => setSelectedMeter(null)}
                  className="btn btn-outline"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Meters
