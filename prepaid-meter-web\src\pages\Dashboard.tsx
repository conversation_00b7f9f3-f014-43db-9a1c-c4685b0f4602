import { useState, useEffect } from 'react'
import { 
  Zap, 
  AlertTriangle, 
  TrendingUp, 
  DollarSign,
  Activity,
  Users,
  RefreshCw,
  CheckCircle
} from 'lucide-react'
import type { DashboardStats, Meter } from '../types/meter'

interface DashboardProps {
  stats: DashboardStats | null
  meters: Meter[]
}

const Dashboard = ({ stats, meters }: DashboardProps) => {
  const [recentActivity, setRecentActivity] = useState<any[]>([])

  useEffect(() => {
    // Mock recent activity data
    const activities = [
      {
        id: 1,
        type: 'recovery',
        message: 'Recovery completed for meter PM001235',
        timestamp: new Date(),
        status: 'success'
      },
      {
        id: 2,
        type: 'recharge',
        message: 'New recharge: ₦5,000 for meter PM001234',
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
        status: 'success'
      },
      {
        id: 3,
        type: 'alert',
        message: 'Low balance alert for meter PM001236',
        timestamp: new Date(Date.now() - 60 * 60 * 1000),
        status: 'warning'
      },
      {
        id: 4,
        type: 'maintenance',
        message: 'Scheduled maintenance for meter PM001237',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        status: 'info'
      }
    ]
    setRecentActivity(activities)
  }, [])

  if (!stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  const statCards = [
    {
      title: 'Total Meters',
      value: stats.totalMeters.toLocaleString(),
      icon: Zap,
      color: 'bg-blue-500',
      change: '+12%',
      changeType: 'positive'
    },
    {
      title: 'Active Meters',
      value: stats.activeMeters.toLocaleString(),
      icon: CheckCircle,
      color: 'bg-green-500',
      change: '+8%',
      changeType: 'positive'
    },
    {
      title: 'Recovery Needed',
      value: stats.recoveryNeeded.toLocaleString(),
      icon: RefreshCw,
      color: 'bg-orange-500',
      change: '-15%',
      changeType: 'negative'
    },
    {
      title: 'Faulty Meters',
      value: stats.faultyMeters.toLocaleString(),
      icon: AlertTriangle,
      color: 'bg-red-500',
      change: '-5%',
      changeType: 'negative'
    },
    {
      title: 'Monthly Revenue',
      value: `₦${stats.monthlyRevenue.toLocaleString()}`,
      icon: DollarSign,
      color: 'bg-emerald-500',
      change: '+23%',
      changeType: 'positive'
    },
    {
      title: 'Avg. Consumption',
      value: `${stats.averageConsumption.toFixed(1)} kWh`,
      icon: Activity,
      color: 'bg-purple-500',
      change: '+3%',
      changeType: 'positive'
    }
  ]

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'recovery': return RefreshCw
      case 'recharge': return DollarSign
      case 'alert': return AlertTriangle
      case 'maintenance': return Activity
      default: return Activity
    }
  }

  const getActivityColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-100'
      case 'warning': return 'text-orange-600 bg-orange-100'
      case 'error': return 'text-red-600 bg-red-100'
      default: return 'text-blue-600 bg-blue-100'
    }
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
        <h2 className="text-2xl font-bold mb-2">Welcome to MeterPro Dashboard</h2>
        <p className="text-blue-100">
          Monitor and manage your prepaid electricity meters efficiently
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div key={index} className="card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
                  <div className="flex items-center mt-2">
                    <span className={`text-sm font-medium ${
                      stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.change}
                    </span>
                    <span className="text-sm text-gray-500 ml-1">vs last month</span>
                  </div>
                </div>
                <div className={`p-3 rounded-full ${stat.color}`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Recent Activity and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-4">
            {recentActivity.map((activity) => {
              const Icon = getActivityIcon(activity.type)
              return (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className={`p-2 rounded-full ${getActivityColor(activity.status)}`}>
                    <Icon className="h-4 w-4" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900">{activity.message}</p>
                    <p className="text-xs text-gray-500">
                      {activity.timestamp.toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button className="w-full btn btn-primary text-left">
              <RefreshCw className="h-4 w-4 mr-2" />
              Start Recovery Process
            </button>
            <button className="w-full btn btn-secondary text-left">
              <Zap className="h-4 w-4 mr-2" />
              Add New Meter
            </button>
            <button className="w-full btn btn-outline text-left">
              <TrendingUp className="h-4 w-4 mr-2" />
              Generate Report
            </button>
            <button className="w-full btn btn-outline text-left">
              <Users className="h-4 w-4 mr-2" />
              Manage Customers
            </button>
          </div>
        </div>
      </div>

      {/* Meters Status Overview */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Meters Requiring Attention</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Meter ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Balance
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {meters.filter(m => m.status !== 'active').map((meter) => (
                <tr key={meter.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {meter.serialNumber}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {meter.customerName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      meter.status === 'faulty' ? 'bg-red-100 text-red-800' :
                      meter.status === 'recovery-needed' ? 'bg-orange-100 text-orange-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {meter.status.replace('-', ' ')}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ₦{meter.balance.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button className="text-blue-600 hover:text-blue-900">
                      {meter.status === 'recovery-needed' ? 'Start Recovery' : 'Diagnose'}
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
