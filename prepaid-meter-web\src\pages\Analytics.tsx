import { useState } from 'react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js'
import { Line, Bar, Doughnut } from 'react-chartjs-2'
import { Calendar, TrendingUp, Zap, DollarSign } from 'lucide-react'
import type { Meter } from '../types/meter'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
)

interface AnalyticsProps {
  meters: Meter[]
}

const Analytics = ({ meters }: AnalyticsProps) => {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d')

  // Generate mock time series data
  const generateTimeSeriesData = () => {
    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365
    const labels = []
    const consumptionData = []
    const revenueData = []
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }))
      
      // Mock consumption data (kWh)
      const baseConsumption = 150 + Math.random() * 100
      consumptionData.push(Math.round(baseConsumption))
      
      // Mock revenue data (₦)
      const baseRevenue = baseConsumption * 45.5 + Math.random() * 1000
      revenueData.push(Math.round(baseRevenue))
    }
    
    return { labels, consumptionData, revenueData }
  }

  const { labels, consumptionData, revenueData } = generateTimeSeriesData()

  // Consumption trend chart
  const consumptionChartData = {
    labels,
    datasets: [
      {
        label: 'Daily Consumption (kWh)',
        data: consumptionData,
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true,
      },
    ],
  }

  // Revenue chart
  const revenueChartData = {
    labels,
    datasets: [
      {
        label: 'Daily Revenue (₦)',
        data: revenueData,
        backgroundColor: 'rgba(16, 185, 129, 0.8)',
        borderColor: 'rgb(16, 185, 129)',
        borderWidth: 1,
      },
    ],
  }

  // Meter status distribution
  const statusCounts = {
    active: meters.filter(m => m.status === 'active').length,
    inactive: meters.filter(m => m.status === 'inactive').length,
    faulty: meters.filter(m => m.status === 'faulty').length,
    recoveryNeeded: meters.filter(m => m.status === 'recovery-needed').length,
  }

  const statusChartData = {
    labels: ['Active', 'Inactive', 'Faulty', 'Recovery Needed'],
    datasets: [
      {
        data: [statusCounts.active, statusCounts.inactive, statusCounts.faulty, statusCounts.recoveryNeeded],
        backgroundColor: [
          'rgba(16, 185, 129, 0.8)',
          'rgba(107, 114, 128, 0.8)',
          'rgba(239, 68, 68, 0.8)',
          'rgba(245, 158, 11, 0.8)',
        ],
        borderColor: [
          'rgb(16, 185, 129)',
          'rgb(107, 114, 128)',
          'rgb(239, 68, 68)',
          'rgb(245, 158, 11)',
        ],
        borderWidth: 2,
      },
    ],
  }

  // Chart options
  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  }

  const doughnutOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom' as const,
      },
    },
  }

  // Calculate analytics summary
  const totalConsumption = consumptionData.reduce((sum, val) => sum + val, 0)
  const totalRevenue = revenueData.reduce((sum, val) => sum + val, 0)
  const avgDailyConsumption = totalConsumption / consumptionData.length
  const avgDailyRevenue = totalRevenue / revenueData.length

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h2>
          <p className="text-gray-600 mt-1">Monitor consumption patterns and system performance</p>
        </div>
        
        <div className="flex space-x-2 mt-4 sm:mt-0">
          <select 
            className="input"
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as typeof timeRange)}
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Consumption</p>
              <p className="text-2xl font-bold text-gray-900">{totalConsumption.toLocaleString()} kWh</p>
              <p className="text-sm text-green-600 mt-1">+12% vs last period</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Zap className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">₦{totalRevenue.toLocaleString()}</p>
              <p className="text-sm text-green-600 mt-1">+18% vs last period</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Daily Consumption</p>
              <p className="text-2xl font-bold text-gray-900">{avgDailyConsumption.toFixed(1)} kWh</p>
              <p className="text-sm text-blue-600 mt-1">Per day average</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <TrendingUp className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Daily Revenue</p>
              <p className="text-2xl font-bold text-gray-900">₦{avgDailyRevenue.toLocaleString()}</p>
              <p className="text-sm text-orange-600 mt-1">Per day average</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-full">
              <Calendar className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Consumption Trend */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Consumption Trend</h3>
          <Line data={consumptionChartData} options={chartOptions} />
        </div>

        {/* Revenue Chart */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Analysis</h3>
          <Bar data={revenueChartData} options={chartOptions} />
        </div>

        {/* Meter Status Distribution */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Meter Status Distribution</h3>
          <div className="flex justify-center">
            <div className="w-80 h-80">
              <Doughnut data={statusChartData} options={doughnutOptions} />
            </div>
          </div>
        </div>

        {/* Top Consumers */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Consumers</h3>
          <div className="space-y-4">
            {meters
              .sort((a, b) => b.monthlyConsumption - a.monthlyConsumption)
              .slice(0, 5)
              .map((meter, index) => (
                <div key={meter.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                        {index + 1}
                      </div>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{meter.customerName}</p>
                      <p className="text-xs text-gray-500">{meter.serialNumber}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-semibold text-gray-900">
                      {meter.monthlyConsumption.toFixed(1)} kWh
                    </p>
                    <p className="text-xs text-gray-500">
                      ₦{(meter.monthlyConsumption * meter.tariffRate).toLocaleString()}
                    </p>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">System Performance Metrics</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600">98.5%</div>
            <div className="text-sm text-gray-600 mt-1">System Uptime</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600">2.3s</div>
            <div className="text-sm text-gray-600 mt-1">Avg Response Time</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600">99.1%</div>
            <div className="text-sm text-gray-600 mt-1">Data Accuracy</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Analytics
