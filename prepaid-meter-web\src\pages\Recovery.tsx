import { useState, useEffect } from 'react'
import { 
  <PERSON>fresh<PERSON><PERSON>, 
  <PERSON>ert<PERSON>riangle, 
  CheckCircle, 
  Clock, 
  Play, 
  Pause,
  RotateCcw,
  FileText
} from 'lucide-react'
import type { Meter, RecoveryOperation, RecoveryStep } from '../types/meter'

interface RecoveryProps {
  meters: Meter[]
}

const Recovery = ({ meters }: RecoveryProps) => {
  const [recoveryOperations, setRecoveryOperations] = useState<RecoveryOperation[]>([])
  const [selectedMeter, setSelectedMeter] = useState<string>('')
  const [recoveryType, setRecoveryType] = useState<RecoveryOperation['type']>('balance-recovery')
  const [activeOperation, setActiveOperation] = useState<RecoveryOperation | null>(null)

  // Mock recovery operations
  useEffect(() => {
    const mockOperations: RecoveryOperation[] = [
      {
        id: '1',
        meterId: '2',
        type: 'balance-recovery',
        status: 'completed',
        startTime: new Date(Date.now() - 2 * 60 * 60 * 1000),
        endTime: new Date(Date.now() - 1 * 60 * 60 * 1000),
        description: 'Recovering lost balance data from meter PM001235',
        technician: '<PERSON>',
        steps: [
          {
            id: '1',
            title: 'Connect to Meter',
            description: 'Establish communication with the meter',
            status: 'completed',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
          },
          {
            id: '2',
            title: 'Backup Current Data',
            description: 'Create backup of existing meter data',
            status: 'completed',
            timestamp: new Date(Date.now() - 110 * 60 * 1000)
          },
          {
            id: '3',
            title: 'Recover Balance',
            description: 'Restore balance from backup or recalculate',
            status: 'completed',
            timestamp: new Date(Date.now() - 90 * 60 * 1000)
          },
          {
            id: '4',
            title: 'Verify Recovery',
            description: 'Confirm balance recovery is successful',
            status: 'completed',
            timestamp: new Date(Date.now() - 60 * 60 * 1000)
          }
        ],
        notes: 'Successfully recovered ₦2,500 balance'
      },
      {
        id: '2',
        meterId: '3',
        type: 'communication-recovery',
        status: 'in-progress',
        startTime: new Date(Date.now() - 30 * 60 * 1000),
        description: 'Restoring communication with meter PM001236',
        technician: 'Jane Doe',
        steps: [
          {
            id: '1',
            title: 'Diagnose Connection',
            description: 'Check physical and network connections',
            status: 'completed',
            timestamp: new Date(Date.now() - 30 * 60 * 1000)
          },
          {
            id: '2',
            title: 'Reset Communication Module',
            description: 'Reset the meter communication module',
            status: 'in-progress'
          },
          {
            id: '3',
            title: 'Test Connection',
            description: 'Verify communication is restored',
            status: 'pending'
          },
          {
            id: '4',
            title: 'Update Firmware',
            description: 'Update meter firmware if needed',
            status: 'pending'
          }
        ]
      }
    ]
    setRecoveryOperations(mockOperations)
  }, [])

  const metersNeedingRecovery = meters.filter(m => 
    m.status === 'recovery-needed' || m.status === 'faulty'
  )

  const startRecovery = () => {
    if (!selectedMeter) return

    const meter = meters.find(m => m.id === selectedMeter)
    if (!meter) return

    const newOperation: RecoveryOperation = {
      id: Date.now().toString(),
      meterId: selectedMeter,
      type: recoveryType,
      status: 'in-progress',
      startTime: new Date(),
      description: `${recoveryType.replace('-', ' ')} for meter ${meter.serialNumber}`,
      technician: 'Current User',
      steps: getRecoverySteps(recoveryType)
    }

    setRecoveryOperations([newOperation, ...recoveryOperations])
    setActiveOperation(newOperation)
    setSelectedMeter('')
  }

  const getRecoverySteps = (type: RecoveryOperation['type']): RecoveryStep[] => {
    const baseSteps = {
      'balance-recovery': [
        { id: '1', title: 'Connect to Meter', description: 'Establish communication', status: 'in-progress' as const },
        { id: '2', title: 'Backup Data', description: 'Create data backup', status: 'pending' as const },
        { id: '3', title: 'Recover Balance', description: 'Restore balance data', status: 'pending' as const },
        { id: '4', title: 'Verify Recovery', description: 'Confirm success', status: 'pending' as const }
      ],
      'communication-recovery': [
        { id: '1', title: 'Diagnose Connection', description: 'Check connections', status: 'in-progress' as const },
        { id: '2', title: 'Reset Module', description: 'Reset communication', status: 'pending' as const },
        { id: '3', title: 'Test Connection', description: 'Verify communication', status: 'pending' as const },
        { id: '4', title: 'Update Firmware', description: 'Update if needed', status: 'pending' as const }
      ],
      'hardware-recovery': [
        { id: '1', title: 'Physical Inspection', description: 'Check hardware', status: 'in-progress' as const },
        { id: '2', title: 'Component Test', description: 'Test components', status: 'pending' as const },
        { id: '3', title: 'Replace Parts', description: 'Replace faulty parts', status: 'pending' as const },
        { id: '4', title: 'System Test', description: 'Full system test', status: 'pending' as const }
      ],
      'data-recovery': [
        { id: '1', title: 'Data Assessment', description: 'Assess data loss', status: 'in-progress' as const },
        { id: '2', title: 'Backup Recovery', description: 'Recover from backup', status: 'pending' as const },
        { id: '3', title: 'Data Validation', description: 'Validate recovered data', status: 'pending' as const },
        { id: '4', title: 'System Sync', description: 'Sync with system', status: 'pending' as const }
      ]
    }
    return baseSteps[type]
  }

  const getStatusIcon = (status: RecoveryOperation['status']) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'in-progress': return <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />
      case 'failed': return <AlertTriangle className="h-5 w-5 text-red-500" />
      default: return <Clock className="h-5 w-5 text-gray-500" />
    }
  }

  const getStepIcon = (status: RecoveryStep['status']) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'in-progress': return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />
      case 'failed': return <AlertTriangle className="h-4 w-4 text-red-500" />
      default: return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Recovery Operations</h2>
        <p className="text-gray-600 mt-1">Manage meter recovery and restoration processes</p>
      </div>

      {/* Start New Recovery */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Start New Recovery</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="label">Select Meter</label>
            <select 
              className="input"
              value={selectedMeter}
              onChange={(e) => setSelectedMeter(e.target.value)}
            >
              <option value="">Choose a meter...</option>
              {metersNeedingRecovery.map(meter => (
                <option key={meter.id} value={meter.id}>
                  {meter.serialNumber} - {meter.customerName}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="label">Recovery Type</label>
            <select 
              className="input"
              value={recoveryType}
              onChange={(e) => setRecoveryType(e.target.value as RecoveryOperation['type'])}
            >
              <option value="balance-recovery">Balance Recovery</option>
              <option value="communication-recovery">Communication Recovery</option>
              <option value="hardware-recovery">Hardware Recovery</option>
              <option value="data-recovery">Data Recovery</option>
            </select>
          </div>
          
          <div className="flex items-end">
            <button 
              onClick={startRecovery}
              disabled={!selectedMeter}
              className="btn btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Play className="h-4 w-4 mr-2" />
              Start Recovery
            </button>
          </div>
        </div>
      </div>

      {/* Active Operation */}
      {activeOperation && (
        <div className="card border-l-4 border-blue-500">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Active Recovery Operation</h3>
            <div className="flex space-x-2">
              <button className="btn btn-outline btn-sm">
                <Pause className="h-4 w-4 mr-1" />
                Pause
              </button>
              <button className="btn btn-outline btn-sm">
                <RotateCcw className="h-4 w-4 mr-1" />
                Restart
              </button>
            </div>
          </div>
          
          <div className="mb-4">
            <p className="text-sm text-gray-600">{activeOperation.description}</p>
            <p className="text-xs text-gray-500 mt-1">
              Started: {activeOperation.startTime.toLocaleString()}
            </p>
          </div>

          <div className="space-y-3">
            {activeOperation.steps.map((step, index) => (
              <div key={step.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-md">
                {getStepIcon(step.status)}
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-gray-900">{step.title}</h4>
                    <span className="text-xs text-gray-500">Step {index + 1}</span>
                  </div>
                  <p className="text-sm text-gray-600">{step.description}</p>
                  {step.timestamp && (
                    <p className="text-xs text-gray-500 mt-1">
                      {step.timestamp.toLocaleTimeString()}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recovery History */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recovery History</h3>
        <div className="space-y-4">
          {recoveryOperations.map((operation) => {
            const meter = meters.find(m => m.id === operation.meterId)
            return (
              <div key={operation.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(operation.status)}
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">
                        {meter?.serialNumber} - {operation.type.replace('-', ' ')}
                      </h4>
                      <p className="text-sm text-gray-600">{operation.description}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-900 capitalize">{operation.status}</p>
                    <p className="text-xs text-gray-500">
                      {operation.startTime.toLocaleDateString()}
                    </p>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Technician:</span>
                    <p className="font-medium">{operation.technician}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Duration:</span>
                    <p className="font-medium">
                      {operation.endTime 
                        ? `${Math.round((operation.endTime.getTime() - operation.startTime.getTime()) / 60000)} min`
                        : 'In progress'
                      }
                    </p>
                  </div>
                  <div>
                    <span className="text-gray-500">Steps:</span>
                    <p className="font-medium">
                      {operation.steps.filter(s => s.status === 'completed').length}/{operation.steps.length}
                    </p>
                  </div>
                  <div className="flex justify-end">
                    <button className="text-blue-600 hover:text-blue-900 text-sm">
                      <FileText className="h-4 w-4 mr-1 inline" />
                      View Report
                    </button>
                  </div>
                </div>

                {operation.notes && (
                  <div className="mt-3 p-2 bg-green-50 rounded border-l-2 border-green-200">
                    <p className="text-sm text-green-800">{operation.notes}</p>
                  </div>
                )}
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default Recovery
